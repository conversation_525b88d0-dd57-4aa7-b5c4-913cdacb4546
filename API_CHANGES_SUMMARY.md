# 多肉品种管理和等级系统API修改说明

## 修改概述

本次修改主要涉及多肉品种管理模块和等级系统的字段调整，以及相关API接口的更新。

## 数据库表结构变更

### 1. plant_species 表（多肉品种表）

#### 删除的字段：
- `unlock_condition` - 解锁条件（JSON格式）
- `max_level` - 最大等级
- `base_energy_per_level` - 每级基础能量需求

#### 新增的字段：
- `unlock_level` - 解锁等级（INT，默认值：1）
- `need_vip` - 是否需要VIP（BOOLEAN，默认值：false）

#### 保留的字段：
- `id` - 品种ID
- `name` - 品种名称（英文标识）
- `display_name` - 显示名称
- `description` - 品种描述
- `rarity` - 稀有度（common/rare/epic/legendary）
- `growth_stages` - 成长阶段配置（JSON格式）
- `image_urls` - 各阶段图片URL（JSON格式）
- `is_active` - 是否启用
- `created_at` - 创建时间
- `updated_at` - 更新时间

### 2. plant_level_config 表（多肉等级配置表）

#### 删除的字段：
- `attribute_bonus` - 属性加成配置（JSON格式）
- `required_days` - 所需天数
- `special_ability` - 特殊能力描述（JSON格式）
- `description` - 等级描述

#### 修改的字段：
- `unlock_reward` → `coin_reward` - 解锁奖励改为金币奖励

#### 保留的字段：
- `id` - 等级配置ID
- `level` - 等级
- `name` - 等级名称
- `icon` - 等级图标URL
- `required_energy` - 所需能量
- `coin_reward` - 金币奖励（原unlock_reward）
- `is_active` - 是否启用
- `sort_order` - 排序顺序
- `created_at` - 创建时间
- `updated_at` - 更新时间

## API接口变更

### 多肉品种管理接口

#### 1. 获取品种列表
- **接口**: `GET /admin/plants/species`
- **变更**: 响应数据中移除了 `unlock_condition`、`max_level`、`base_energy_per_level` 字段，新增了 `unlock_level` 和 `need_vip` 字段

#### 2. 创建品种
- **接口**: `POST /admin/plants/species`
- **变更**: 
  - 请求参数移除：`unlock_condition`、`max_level`、`base_energy_per_level`
  - 请求参数新增：`unlock_level`（解锁等级）、`need_vip`（是否需要VIP）

#### 3. 更新品种
- **接口**: `PUT /admin/plants/species/{id}`
- **变更**: 
  - 请求参数移除：`unlock_condition`、`max_level`、`base_energy_per_level`
  - 请求参数新增：`unlock_level`、`need_vip`

#### 4. 获取品种详情 【新增接口】
- **接口**: `GET /admin/plants/species/{id}`
- **说明**: 新增的品种详情弹窗接口
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "succulent_basic",
    "display_name": "基础多肉",
    "description": "最基础的多肉品种",
    "rarity": "common",
    "unlock_level": 1,
    "need_vip": false,
    "growth_stages": {...},
    "image_urls": {...},
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": null
  }
}
```

### 等级系统接口

#### 1. 获取等级配置列表
- **接口**: `GET /admin/plants/levels`
- **变更**: 响应数据中移除了 `attribute_bonus`、`required_days`、`special_ability`、`description` 字段，`unlock_reward` 改为 `coin_reward`

#### 2. 创建等级配置
- **接口**: `POST /admin/plants/levels`
- **变更**: 
  - 请求参数移除：`required_days`、`attribute_bonus`、`special_ability`、`description`
  - 请求参数修改：`unlock_reward` → `coin_reward`

#### 3. 更新等级配置
- **接口**: `PUT /admin/plants/levels/{id}`
- **变更**: 
  - 请求参数移除：`required_days`、`attribute_bonus`、`special_ability`、`description`
  - 请求参数修改：`unlock_reward` → `coin_reward`

#### 4. 获取等级名称列表 【新增接口】
- **接口**: `GET /admin/plants/levels/names`
- **说明**: 用于多肉品种管理中成长阶段选择，自动填充所需能量
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "level": 1,
      "name": "幼苗",
      "required_energy": 100
    },
    {
      "id": 2,
      "level": 2,
      "name": "小苗",
      "required_energy": 300
    }
  ]
}
```

## 前端需要调整的地方

### 1. 多肉品种管理页面

#### 表单字段调整：
- **移除字段**：解锁条件、最大等级、基础能量字段
- **新增字段**：
  - 解锁等级（数字输入框，默认值：1）
  - 是否需要VIP（开关/复选框，默认值：false）

#### 成长阶段选择：
- 调用新接口 `GET /admin/plants/levels/names` 获取等级名称列表
- 成长阶段只能选择等级系统中的名称（不需要前面的等级数字）
- 选择等级名称后自动填充所需能量

#### 详情弹窗：
- 新增详情弹窗功能，调用 `GET /admin/plants/species/{id}` 接口

### 2. 等级系统管理页面

#### 表单字段调整：
- **移除字段**：属性加成、所需天数、等级数值、特殊能力、等级描述
- **字段重命名**：解锁奖励 → 金币奖励

## 注意事项

1. 所有涉及旧字段的前端代码需要更新
2. 数据库已完成迁移，备份表为 `plant_species_backup` 和 `plant_level_config_backup`
3. 新增的接口需要相应的权限验证（super_admin 或 admin）
4. 建议在前端添加相应的表单验证逻辑

## 测试建议

1. 测试多肉品种的创建、编辑、详情查看功能
2. 测试等级系统的管理功能
3. 测试成长阶段选择和能量自动填充功能
4. 验证新字段的数据保存和显示是否正常
